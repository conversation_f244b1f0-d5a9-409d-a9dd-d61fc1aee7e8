// shared/utils/transformUserData.ts - Utility functions for transforming user data
import { UserDetailsData } from '@/shared/types/user-management-types';
import { EditUserData } from '@/shared/query';
import { User } from '@/shared/types/global';

/**
 * IMPORTANT: This project uses three different user data structures:
 *
 * 1. User (from global.ts) - Authenticated user data (snake_case: first_name, last_name, phone_verified, agent_name)
 *    Used in: auth store, profile components, header components
 *
 * 2. UserData (from user-management-types.ts) - User list data (camelCase: firstName, lastName, phoneVerified)
 *    Used in: user management table, user list queries
 *
 * 3. UserDetailsData (from user-management-types.ts) - Detailed user data (camelCase: firstName, lastName, phoneVerified)
 *    Used in: user details pages, user edit forms, user details queries
 *
 * Always use the correct interface for the context you're working in!
 */

/**
 * Transforms login response user data from snake_case to camelCase
 * This ensures all user data stored in auth store follows a consistent camelCase format
 *
 * @param loginUser - User data from login API response (snake_case)
 * @param tenantId - Optional tenant ID to add to user data
 * @returns User data in camelCase format for auth store
 */
export const transformLoginUserData = (loginUser: any, tenantId?: number): User => {
  return {
    id: loginUser.id,
    firstName: loginUser.first_name,
    lastName: loginUser.last_name,
    phone: loginUser.phone,
    phoneVerified: loginUser.phone_verified,
    parentType: loginUser.parent_type,
    parentId: loginUser.parent_id,
    email: loginUser.email,
    resetPasswordToken: loginUser.reset_password_token,
    resetPasswordSentAt: loginUser.reset_password_sent_at,
    rememberCreatedAt: loginUser.remember_created_at,
    confirmationToken: loginUser.confirmation_token,
    confirmedAt: loginUser.confirmed_at,
    confirmationSentAt: loginUser.confirmation_sent_at,
    unconfirmedEmail: loginUser.unconfirmed_email,
    createdAt: loginUser.created_at,
    updatedAt: loginUser.updated_at,
    tenantId: tenantId || loginUser.tenant_id,
    agentName: loginUser.agent_name,
    active: loginUser.active,
    deactivatedById: loginUser.deactivated_by_id,
    deactivatedByType: loginUser.deactivated_by_type,
    deactivatedAt: loginUser.deactivated_at,
    allowedCurrencies: loginUser.allowed_currencies,
    kycRegulated: loginUser.kyc_regulated,
    loginCount: loginUser.login_count,
    secretKey: loginUser.secret_key,
    affiliateToken: loginUser.affiliate_token,
    ipWhitelist: loginUser.ip_whitelist,
    isAppliedIpWhitelist: loginUser.is_applied_ip_whitelist,
    ipWhitelistType: loginUser.ip_whitelist_type,
    timezone: loginUser.timezone,
    agentType: loginUser.agent_type,
    agentAction: loginUser.agent_action,
    affiliateStatus: loginUser.affiliate_status,
    reportingEmail: loginUser.reporting_email,
    reportingEmailVerified: loginUser.reporting_email_verified,
    roles: loginUser.roles,
    internalPlayerID: loginUser.internalPlayerID,
  } as User;
};

/**
 * Transforms UserDetailsData to EditUserData format for form editing
 * This function maps the user details response to the format expected by the edit user form
 * 
 * @param userData - User details data from the API
 * @returns EditUserData formatted for form editing
 */
export const transformUserDataForEdit = (userData: UserDetailsData): EditUserData => {
  return {
    id: Number(userData.id),
    userName: userData.userName || '',
    nickName: '', // Not available in UserDetailsData
    firstName: userData.firstName || '',
    lastName: userData.lastName || '',
    email: userData.email || '',
    phone: userData.phone || '',
    phoneCode: userData.phoneCode || '+94',
    zipCode: '', // Not available in UserDetailsData
    dateOfBirth: '', // Not available in UserDetailsData
    countryCode: 'LK', // Default value
    currencyId: Number(userData.currencyId) || 1,
    activeBonusId: null, // Not available in UserDetailsData
    vipLevel: Number(userData.playerCategoryLevel) || 1,
    city: '', // Not available in UserDetailsData
    emailVerified: userData.emailVerified || false,
    phoneVerified: userData.phoneVerified || false,
    forceResetPassword: userData.forceResetPassword || false,
    markAsBot: false, // Not available in UserDetailsData
    active: userData.active !== false,
    demo: userData.demo || false,
    affiliatedData: '', // Not available in UserDetailsData
    nationalId: null, // Not available in UserDetailsData
    clickId: null, // Not available in UserDetailsData
    wyntaClickId: null, // Not available in UserDetailsData
    categoryType: null, // Not available in UserDetailsData
    userType: userData.userType || 1,
    encryptedPassword: '', // Don't pre-fill password for edit
    rfidToken: (userData as any).rfidToken || '', // Not available in UserDetailsData yet
  };
};
